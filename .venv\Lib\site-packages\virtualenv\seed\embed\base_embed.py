from __future__ import annotations

import logging
from abc import ABC
from argparse import SUPPRESS
from pathlib import Path

from virtualenv.seed.seeder import Seeder
from virtualenv.seed.wheels import Version

LOGGER = logging.getLogger(__name__)
PERIODIC_UPDATE_ON_BY_DEFAULT = True


class BaseEmbed(Seeder, ABC):
    def __init__(self, options) -> None:
        super().__init__(options, enabled=options.no_seed is False)

        self.download = options.download
        self.extra_search_dir = [i.resolve() for i in options.extra_search_dir if i.exists()]

        self.pip_version = options.pip
        self.setuptools_version = options.setuptools

        # wheel version needs special handling
        # on Python > 3.8, the default is None (as in not used)
        # so we can differentiate between explicit and implicit none
        self.wheel_version = options.wheel or "none"

        self.no_pip = options.no_pip
        self.no_setuptools = options.no_setuptools
        self.no_wheel = options.no_wheel
        self.app_data = options.app_data
        self.periodic_update = not options.no_periodic_update

        if options.py_version[:2] >= (3, 9):
            if options.wheel is not None or options.no_wheel:
                LOGGER.warning(
                    "The --no-wheel and --wheel options are deprecated. "
                    "They have no effect for Python > 3.8 as wheel is no longer "
                    "bundled in virtualenv.",
                )
            self.no_wheel = True

        if not self.distribution_to_versions():
            self.enabled = False

    @classmethod
    def distributions(cls) -> dict[str, Version]:
        return {
            "pip": Version.bundle,
            "setuptools": Version.bundle,
            "wheel": Version.bundle,
        }

    def distribution_to_versions(self) -> dict[str, str]:
        return {
            distribution: getattr(self, f"{distribution}_version")
            for distribution in self.distributions()
            if getattr(self, f"no_{distribution}", None) is False and getattr(self, f"{distribution}_version") != "none"
        }

    @classmethod
    def add_parser_arguments(cls, parser, interpreter, app_data):  # noqa: ARG003
        group = parser.add_mutually_exclusive_group()
        group.add_argument(
            "--no-download",
            "--never-download",
            dest="download",
            action="store_false",
            help=f"pass to disable download of the latest {'/'.join(cls.distributions())} from PyPI",
            default=True,
        )
        group.add_argument(
            "--download",
            dest="download",
            action="store_true",
            help=f"pass to enable download of the latest {'/'.join(cls.distributions())} from PyPI",
            default=False,
        )
        parser.add_argument(
            "--extra-search-dir",
            metavar="d",
            type=Path,
            nargs="+",
            help="a path containing wheels to extend the internal wheel list (can be set 1+ times)",
            default=[],
        )
        for distribution, default in cls.distributions().items():
            help_ = f"version of {distribution} to install as seed: embed, bundle, none or exact version"
            if interpreter.version_info[:2] >= (3, 12) and distribution in {"wheel", "setuptools"}:
                default = "none"  # noqa: PLW2901
            if interpreter.version_info[:2] >= (3, 9) and distribution == "wheel":
                default = None  # noqa: PLW2901
                help_ = SUPPRESS
            parser.add_argument(
                f"--{distribution}",
                dest=distribution,
                metavar="version",
                help=help_,
                default=default,
            )
        for distribution in cls.distributions():
            help_ = f"do not install {distribution}"
            if interpreter.version_info[:2] >= (3, 9) and distribution == "wheel":
                help_ = SUPPRESS
            parser.add_argument(
                f"--no-{distribution}",
                dest=f"no_{distribution}",
                action="store_true",
                help=help_,
                default=False,
            )
        parser.add_argument(
            "--no-periodic-update",
            dest="no_periodic_update",
            action="store_true",
            help="disable the periodic (once every 14 days) update of the embedded wheels",
            default=not PERIODIC_UPDATE_ON_BY_DEFAULT,
        )

    def __repr__(self) -> str:
        result = self.__class__.__name__
        result += "("
        if self.extra_search_dir:
            result += f"extra_search_dir={', '.join(str(i) for i in self.extra_search_dir)},"
        result += f"download={self.download},"
        for distribution in self.distributions():
            if getattr(self, f"no_{distribution}", None):
                continue
            version = getattr(self, f"{distribution}_version", None)
            if version == "none":
                continue
            ver = f"={version or 'latest'}"
            result += f" {distribution}{ver},"
        return result[:-1] + ")"


__all__ = [
    "BaseEmbed",
]
