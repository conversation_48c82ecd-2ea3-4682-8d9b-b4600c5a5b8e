Metadata-Version: 2.1
Name: codecov
Version: 2.1.13
Summary: Hosted coverage reports for GitHub, Bitbucket and Gitlab
Home-page: https://github.com/codecov/codecov-python
Author: Codecov
Author-email: <EMAIL>
License: Apache 2.0
Keywords: coverage codecov code python java scala php
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Plugins
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Topic :: Software Development :: Testing
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*
License-File: LICENSE
Requires-Dist: requests (>=2.7.9)
Requires-Dist: coverage

UNKNOWN

