Metadata-Version: 2.1
Name: rootpath
Version: 0.1.1
Summary: Python project/package root path detection.
Home-page: https://github.com/grimen/python-rootpath
Author: <PERSON>lt
Author-email: <EMAIL>
License: MIT
Download-URL: https://github.com/grimen/python-rootpath
Project-URL: repository, https://github.com/grimen/python-rootpath
Project-URL: bugs, https://github.com/grimen/python-rootpath/issues
Keywords: python,utlity,common,root,rootpath,root-path,detect,autodetect,auto-detect,project-root,project-root-path,package-root,package-root-path
Platform: UNKNOWN
Classifier: Topic :: Software Development :: Libraries
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Description-Content-Type: text/markdown
Requires-Dist: six (>=1.11.0)
Requires-Dist: coloredlogs (>=10.0)
Requires-Dist: termcolor (>=1.1.0)
Requires-Dist: colour-runner (>=0.0.5)
Requires-Dist: deepdiff (>=3.3.0)
Requires-Dist: pygments (>=2.2.0)
Requires-Dist: tox (>=3.0.0)
Requires-Dist: coverage (>=4.5.2)
Requires-Dist: codecov (>=2.0.15)


# `rootpath` [![PyPI version](https://badge.fury.io/py/rootpath.svg)](https://badge.fury.io/py/rootpath) [![Build Status](https://travis-ci.com/grimen/python-rootpath.svg?branch=master)](https://travis-ci.com/grimen/python-rootpath) [![Coverage Status](https://codecov.io/gh/grimen/python-rootpath/branch/master/graph/badge.svg)](https://codecov.io/gh/grimen/python-rootpath)

*Python project/package root path detection.*


## Introduction

Auto-magic project/package root path detection - from a child module file for Python libraries/projects.

It does this by detecting typical package/project root files/folders (e.g. `.git`, `requirements.txt`, etc.), but it can also be overriden easily if needed.

As a little bonus it exposes an optional helper for adding root path to the Python load path (`sys.path`) for resolving Python module import hell (which is terribly broken by design).


## Install

Install using **pip**:

```sh
pip install rootpath
```


## Use: Basic

Detect a project/package root path:

**1.** Assuming we have a **python** library/application project...

```
/home/<USER>/projects
    └── py-foo
            └── foo
                └── utils
                    └── __init__.py
                    └── baz.py
                    └── say.py
                └── __init__.py
                └── bar.py
            README.md
            requirements.txt
            setup.py
```

`foo/bar.py` - top level package module

```python
import rootpath

def bar():
    path = rootpath.detect()

    assert path == '/home/<USER>/projects/py-foo'

    print('---')
    print('FILE:', __file__)
    print('ROOT:', path)
    print('---')

if __name__ == '__main__':
    bar()
```

`foo/utils/baz.py` - nested level package module (dependency)

```python
import rootpath

def baz():
    path = rootpath.detect()

    assert path == '/home/<USER>/projects/py-foo'

    print('---')
    print('FILE:', __file__)
    print('ROOT:', path)
    print('---')

if __name__ == '__main__':
    baz()
```

`foo/utils/say.py` - nested level package module (dependency)

```python
import rootpath

def say():
    print('---')
    print('SAY: {0}'.format(rootpath.detect()))
    print('---')

if __name__ == '__main__':
    say()
```

**2.** Let's run the files individually - they should both with successful assertions and output accurately detected root paths...

```sh
$ cd /home/<USER>/projects/py-foo

$ python ./foo/bar.py

---
FILE: /home/<USER>/projects/py-foo/foo/bar.py
ROOT: /home/<USER>/projects/py-foo
---

$ python ./foo/utils/baz.py

---
FILE: /home/<USER>/projects/py-foo/foo/utils/baz.py
ROOT: /home/<USER>/projects/py-foo
---

$ python ./foo/utils/say.py

---
SAY: /home/<USER>/projects/py-foo
---

```


## Use: Painless Python module imports

Using the above example code project as a reference, as and example to enable painless Python module imports:

**1.** Let's make use of the load path helper in the higher order modules...

`foo/bar.py`

```python
import rootpath

# 1. prepends root path to `sys.path`
rootpath.append()

# 2. will import correctly without errors no matter if imported/executed from same path or any other system path - which is not true for the native Python 3 relative import
import rootpath.utils.say as say

def bar():
    say()

if __name__ == '__main__':
    bar()
```

`foo/utils/baz.py`

```python
import rootpath

# 1. prepends root path to `sys.path`
rootpath.append()

# 2. will import correctly without errors no matter if imported/executed from same path or any other system path - which is not true for the native Python 3 relative import
import rootpath.utils.say as say

def baz():
    hello()

if __name__ == '__main__':
    baz()
```

**2.** Let's run the files individually - `say` module should be imported correctly without any errors from any module path namespace...

```sh
$ cd /home/<USER>/projects/py-foo

$ python ./foo/bar.py

---
SAY: /home/<USER>/projects/py-foo
---

$ python ./foo/utils/baz.py

---
SAY: /home/<USER>/projects/py-foo
---

$ python ./foo/utils/say.py

---
SAY: /home/<USER>/projects/py-foo
---

$ cd /home/<USER>/projects/py-foo/foo

$ python ./bar.py
---
SAY: /home/<USER>/projects/py-foo
---

$ python ./utils/baz.py
---
SAY: /home/<USER>/projects/py-foo
---

$ python ./utils/say.py

---
SAY: /home/<USER>/projects/py-foo
---

$ cd /home/<USER>/projects/py-foo/foo/utils

$ python ./utils/baz.py

---
SAY: /home/<USER>/projects/py-foo
---

$ python ./utils/say.py

---
SAY: /home/<USER>/projects/py-foo
---
```


## About

This project was mainly initiated - in lack of well tested and reliable existing alternatives - to be used at our work at **[Markable.ai](https://markable.ai)** to have common code conventions between various programming environments where **Python** (research, CV, AI) is heavily used.


## License

Released under the MIT license.


